import { HomeLayout } from "@/app/layouts/HomeLayout";
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/app/components/ui/card";
import { RequestInfo } from "rwsdk/worker";

export default function Dashboard(props: RequestInfo) {
  const { ctx } = props;

  return (
    <HomeLayout {...props}>
      <div className="container mx-auto px-4 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">SUPERUSER Dashboard</h1>
          <p className="text-gray-600 mt-2">
            Welcome, {ctx.user?.username}. You have SUPERUSER access.
          </p>
        </div>

        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          <Card>
            <CardHeader>
              <CardTitle>Organization Management</CardTitle>
              <CardDescription>
                Create and manage organizations
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-gray-600">
                Coming soon: Organization CRUD functionality
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>User Management</CardTitle>
              <CardDescription>
                Manage user roles and permissions
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-gray-600">
                Coming soon: User management features
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>System Overview</CardTitle>
              <CardDescription>
                View system statistics and health
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-gray-600">
                Coming soon: System monitoring
              </p>
            </CardContent>
          </Card>
        </div>

        <div className="mt-8">
          <Card>
            <CardHeader>
              <CardTitle>Authentication Test</CardTitle>
              <CardDescription>
                Verify SUPERUSER authentication is working
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <p><strong>User ID:</strong> {ctx.user?.id}</p>
                <p><strong>Username:</strong> {ctx.user?.username}</p>
                <p><strong>Role:</strong> {ctx.user?.role}</p>
                <p><strong>Email:</strong> {ctx.user?.email || "Not set"}</p>
                <div className="mt-4 p-3 bg-green-50 border border-green-200 rounded-md">
                  <p className="text-green-800 text-sm">
                    ✅ SUPERUSER authentication is working correctly!
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </HomeLayout>
  );
}
