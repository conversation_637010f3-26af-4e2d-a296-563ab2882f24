import { HomeLayout } from "@/app/layouts/HomeLayout";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/app/components/ui/card";
import { Button } from "@/app/components/ui/button";
import { Input } from "@/app/components/ui/input";
import { Textarea } from "@/app/components/ui/textarea";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from "@/app/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/app/components/ui/dialog";
import { Eye, Edit, Trash2, Plus } from "lucide-react";
import { RequestInfo } from "rwsdk/worker";

export default function Dashboard(props: RequestInfo) {
  const { ctx } = props;

  // Mock data for organizations - will be replaced with real data later
  const organizations: Array<{
    id: string;
    name: string;
    memberCount: number;
    owner: string;
  }> = [];

  return (
    <HomeLayout {...props}>
      <div className="container mx-auto px-4 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">SUPERUSER Dashboard</h1>
          <p className="text-gray-600 mt-2">
            Welcome, {ctx.user?.username}. You have SUPERUSER access.
          </p>
        </div>

        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          <Card>
            <CardHeader>
              <CardTitle>Organization Management</CardTitle>
              <CardDescription>
                Create and manage organizations
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-gray-600">
                Coming soon: Organization CRUD functionality
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>User Management</CardTitle>
              <CardDescription>
                Manage user roles and permissions
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-gray-600">
                Coming soon: User management features
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>System Overview</CardTitle>
              <CardDescription>
                View system statistics and health
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-gray-600">
                Coming soon: System monitoring
              </p>
            </CardContent>
          </Card>
        </div>

        <div className="mt-8">
          <Card>
            <CardHeader>
              <CardTitle>Authentication Test</CardTitle>
              <CardDescription>
                Verify SUPERUSER authentication is working
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <p><strong>User ID:</strong> {ctx.user?.id}</p>
                <p><strong>Username:</strong> {ctx.user?.username}</p>
                <p><strong>Role:</strong> {ctx.user?.role}</p>
                <p><strong>Email:</strong> {ctx.user?.email || "Not set"}</p>
                <div className="mt-4 p-3 bg-green-50 border border-green-200 rounded-md">
                  <p className="text-green-800 text-sm">
                    ✅ SUPERUSER authentication is working correctly!
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Organizations Management Section */}
        <div className="mt-8">
          <div className="flex justify-between items-center mb-6">
            <div>
              <h2 className="text-2xl font-bold text-gray-900">Organizations</h2>
              <p className="text-gray-600 mt-1">
                Manage sailing organizations and their members
              </p>
            </div>
            <Dialog>
              <DialogTrigger asChild>
                <Button>
                  <Plus className="mr-2 h-4 w-4" />
                  Create Organization
                </Button>
              </DialogTrigger>
              <DialogContent className="sm:max-w-[425px]">
                <DialogHeader>
                  <DialogTitle>Create New Organization</DialogTitle>
                  <DialogDescription>
                    Add a new sailing organization to the system. You can assign administrators and manage members after creation.
                  </DialogDescription>
                </DialogHeader>
                <div className="grid gap-4 py-4">
                  <div className="grid gap-2">
                    <label htmlFor="name" className="text-sm font-medium">
                      Organization Name *
                    </label>
                    <Input
                      id="name"
                      placeholder="e.g., Sailing Club Norway"
                      className="col-span-3"
                    />
                  </div>
                  <div className="grid gap-2">
                    <label htmlFor="description" className="text-sm font-medium">
                      Description
                    </label>
                    <Textarea
                      id="description"
                      placeholder="Brief description of the organization..."
                      className="col-span-3"
                    />
                  </div>
                </div>
                <DialogFooter>
                  <Button type="submit">Create Organization</Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
          </div>

          {/* Organizations Table */}
          <Card>
            <CardContent className="p-0">
              {/* Mock data - will be replaced with real data later */}
              {organizations.length === 0 ? (
                <div className="p-8 text-center">
                  <div className="mx-auto max-w-sm">
                    <h3 className="text-lg font-medium text-gray-900 mb-2">
                      No organizations yet
                    </h3>
                    <p className="text-gray-600 mb-4">
                      Get started by creating your first sailing organization.
                    </p>
                    <Dialog>
                      <DialogTrigger asChild>
                        <Button>
                          <Plus className="mr-2 h-4 w-4" />
                          Create Your First Organization
                        </Button>
                      </DialogTrigger>
                      <DialogContent className="sm:max-w-[425px]">
                        <DialogHeader>
                          <DialogTitle>Create New Organization</DialogTitle>
                          <DialogDescription>
                            Add a new sailing organization to the system. You can assign administrators and manage members after creation.
                          </DialogDescription>
                        </DialogHeader>
                        <div className="grid gap-4 py-4">
                          <div className="grid gap-2">
                            <label htmlFor="name-empty" className="text-sm font-medium">
                              Organization Name *
                            </label>
                            <Input
                              id="name-empty"
                              placeholder="e.g., Sailing Club Norway"
                              className="col-span-3"
                            />
                          </div>
                          <div className="grid gap-2">
                            <label htmlFor="description-empty" className="text-sm font-medium">
                              Description
                            </label>
                            <Textarea
                              id="description-empty"
                              placeholder="Brief description of the organization..."
                              className="col-span-3"
                            />
                          </div>
                        </div>
                        <DialogFooter>
                          <Button type="submit">Create Organization</Button>
                        </DialogFooter>
                      </DialogContent>
                    </Dialog>
                  </div>
                </div>
              ) : (
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Organization Name</TableHead>
                      <TableHead>Members</TableHead>
                      <TableHead>Owner</TableHead>
                      <TableHead className="text-right">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {organizations.map((org) => (
                      <TableRow key={org.id}>
                        <TableCell className="font-medium">{org.name}</TableCell>
                        <TableCell>{org.memberCount}</TableCell>
                        <TableCell>{org.owner}</TableCell>
                        <TableCell className="text-right">
                          <div className="flex justify-end gap-2">
                            <Button variant="ghost" size="sm">
                              <Eye className="h-4 w-4" />
                              <span className="sr-only">View organization</span>
                            </Button>
                            <Button variant="ghost" size="sm">
                              <Edit className="h-4 w-4" />
                              <span className="sr-only">Edit organization</span>
                            </Button>
                            <Button variant="ghost" size="sm">
                              <Trash2 className="h-4 w-4" />
                              <span className="sr-only">Delete organization</span>
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </HomeLayout>
  );
}
