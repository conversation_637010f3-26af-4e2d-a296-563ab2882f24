"use server";

import { db } from "@/db";
import {
  createOrganization,
  getAllOrganizations,
  updateOrganization,
  deleteOrganization,
  getOrganization
} from "../admin/functions";

// Re-export the organization functions
export {
  createOrganization,
  updateOrganization,
  deleteOrganization,
  getOrganization
};

// Helper function to format organizations for display
export async function getOrganizationsForDisplay() {
  try {
    const organizations = await getAllOrganizations();

    // Transform the data for the dashboard display
    return organizations.map(org => {
      // Find the admin/owner (first admin member)
      const adminMember = org.members.find(member => member.role === "ADMIN");

      return {
        id: org.id,
        name: org.name,
        description: org.description,
        memberCount: org._count.members,
        owner: adminMember?.user.username || "No admin assigned",
        createdAt: org.createdAt,
        updatedAt: org.updatedAt
      };
    });
  } catch (error) {
    console.error("Error formatting organizations for display:", error);
    return [];
  }
}
