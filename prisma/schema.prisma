// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider = "prisma-client"

  runtime                = "workerd"
  moduleFormat           = "esm"
  generatedFileExtension = "ts"
  importFileExtension    = "ts"

  output          = "../generated/prisma"
  previewFeatures = ["queryCompiler", "driverAdapters"]
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

model User {
  id             String          @id @default(uuid()) // User ID (UUID-based)
  username       String          @unique
  email          String?         @unique
  password       String?
  club           String?
  createdAt      DateTime        @default(now())
  updatedAt      DateTime?       @updatedAt
  role           Role            @default(USER)
  profile        Profile?
  organizations OrganizationMembership[]
  posts          Post[]
  comments       Comment[]
  credentials    Credential[] // Relationship: One user can have many credentials
  passwordResets PasswordReset[]
}

model Credential {
  id           String   @id @default(uuid()) // Internal DB ID
  userId       String   @unique // Every credential is linked to a specific user
  user         User     @relation(fields: [userId], references: [id])
  createdAt    DateTime @default(now())
  deviceName   String? // Human-readable name or identifier for the device/authenticator
  credentialId String   @unique // WebAuthn credential identifier
  publicKey    Bytes
  counter      Int      @default(0)

  @@index([credentialId])
  @@index([userId])
}

model Organization {
  id          String   @id @default(uuid())
  name        String
  description String?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  members     OrganizationMembership[]
}

model OrganizationMembership {
  id             String       @id @default(uuid())
  user           User         @relation(fields: [userId], references: [id])
  userId         String
  organization   Organization @relation(fields: [organizationId], references: [id])
  organizationId String
  role           String       // "ADMIN", "COACH", "MEMBER"
  joinedAt       DateTime     @default(now())

  @@unique([userId, organizationId])
}

model Profile {
  id              String  @id @default(uuid())
  userId          String  @unique
  user            User    @relation(fields: [userId], references: [id])
  name            String?
  bio             String?
  location        String?
  experienceLevel String?
  profilePicture  String?
  privacySettings String? // JSON string for privacy settings

  // Sailing-specific fields
  sailingExperience String? // Years of experience or description
  certifications    String? // JSON array of certifications
  boatInformation   String? // JSON object with boat details
  clubAffiliation   String? // Sailing club membership

  // Timestamps
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model Post {
  id          String     @id @default(uuid())
  userId      String
  user        User       @relation(fields: [userId], references: [id])
  title       String
  content     String
  category    String?
  status      PostStatus @default(DRAFT)
  publishedAt DateTime?
  createdAt   DateTime   @default(now())
  updatedAt   DateTime   @updatedAt
  comments    Comment[]
}

enum PostStatus {
  DRAFT
  PUBLISHED
  ARCHIVED
}

model Comment {
  id        String   @id @default(uuid())
  userId    String
  user      User     @relation(fields: [userId], references: [id])
  postId    String
  post      Post     @relation(fields: [postId], references: [id])
  content   String
  createdAt DateTime @default(now())
}

enum Role {
  USER
  SUPERUSER
}

model PasswordReset {
  id        String   @id @default(cuid())
  userId    String
  token     String   @unique
  expires   DateTime
  createdAt DateTime @default(now())
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)
}
